<!-- templates/dashboard.html -->
{% extends "base.html" %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">Dashboard</h1>
</div>

<div class="row">
    <!-- Status Cards -->
    <div class="col-md-3">
        <div class="card mb-4">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="card-title text-muted">Total Agents</h6>
                        <h3 class="card-text">
                            {{ status_counts|sum(attribute='count') }}
                        </h3>
                    </div>
                    <i class="fas fa-desktop fa-2x" style="color: #8a2be2;"></i>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3">
        <div class="card mb-4">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="card-title text-muted">Active Agents</h6>
                        <h3 class="card-text">
                            {% for status in status_counts %}
                                {% if status.status == 'active' %}
                                    {{ status.count }}
                                {% endif %}
                            {% endfor %}
                        </h3>
                    </div>
                    <i class="fas fa-check-circle fa-2x" style="color: #198754;"></i>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3">
        <div class="card mb-4">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="card-title text-muted">Offline Agents</h6>
                        <h3 class="card-text">
                            {% for status in status_counts %}
                                {% if status.status == 'offline' %}
                                    {{ status.count }}
                                {% endif %}
                            {% endfor %}
                        </h3>
                    </div>
                    <i class="fas fa-times-circle fa-2x" style="color: #6c757d;"></i>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3">
        <div class="card mb-4">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="card-title text-muted">Pending Tasks</h6>
                        <h3 class="card-text">
                            {{ recent_tasks|selectattr('status', 'equalto', 'pending')|list|length }}
                        </h3>
                    </div>
                    <i class="fas fa-tasks fa-2x" style="color: #ffc107;"></i>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- Recent Tasks -->
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">Recent Tasks</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Agent</th>
                                <th>Command</th>
                                <th>Status</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for task in recent_tasks %}
                            <tr>
                                <td>{{ task.hostname }}</td>
                                <td class="text-truncate" style="max-width: 150px;">{{ task.command }}</td>
                                <td>
                                    {% if task.status == 'completed' %}
                                        <span class="status-badge status-active">Completed</span>
                                    {% elif task.status == 'executing' %}
                                        <span class="status-badge" style="background-color: #ffc107; color: black;">Executing</span>
                                    {% else %}
                                        <span class="status-badge status-offline">Pending</span>
                                    {% endif %}
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Send Command -->
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">Send Command</h5>
            </div>
            <div class="card-body">
                <form method="POST" action="{{ url_for('send_command') }}">
                    <div class="mb-3">
                        <label for="agent_id" class="form-label">Select Agent</label>
                        <select class="form-select" id="agent_id" name="agent_id" required>
                            <option value="all">All Agents</option>
                            {% for agent in agents %}
                                <option value="{{ agent.id }}">{{ agent.hostname }} ({{ agent.ip }})</option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="command" class="form-label">Command</label>
                        <input type="text" class="form-control" id="command" name="command" placeholder="Enter command" required>
                    </div>
                    <button type="submit" class="btn btn-primary">Execute</button>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}