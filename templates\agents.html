<!-- templates/agents.html -->
{% extends "base.html" %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">Agents Management</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <button type="button" class="btn btn-sm btn-outline-secondary" data-bs-toggle="modal" data-bs-target="#agentStatsModal">
                <i class="fas fa-chart-pie me-1"></i> View Stats
            </button>
        </div>
    </div>
</div>

<!-- Stats Modal -->
<div class="modal fade" id="agentStatsModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Agent Statistics</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-md-6">
                        <div class="card mb-3">
                            <div class="card-header">
                                <h6 class="card-title mb-0">Platform Distribution</h6>
                            </div>
                            <div class="card-body">
                                <ul class="list-group list-group-flush">
                                    {% for stat in platform_stats %}
                                    <li class="list-group-item d-flex justify-content-between align-items-center bg-transparent text-light border-secondary">
                                        {{ stat.platform or 'Unknown' }}
                                        <span class="badge bg-primary rounded-pill">{{ stat.count }}</span>
                                    </li>
                                    {% endfor %}
                                </ul>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="card-title mb-0">Status Overview</h6>
                            </div>
                            <div class="card-body">
                                <ul class="list-group list-group-flush">
                                    <li class="list-group-item d-flex justify-content-between align-items-center bg-transparent text-light border-secondary">
                                        Active Agents
                                        <span class="badge bg-success rounded-pill">{{ agents|selectattr('status', 'equalto', 'active')|list|length }}</span>
                                    </li>
                                    <li class="list-group-item d-flex justify-content-between align-items-center bg-transparent text-light border-secondary">
                                        Offline Agents
                                        <span class="badge bg-secondary rounded-pill">{{ agents|selectattr('status', 'equalto', 'offline')|list|length }}</span>
                                    </li>
                                    <li class="list-group-item d-flex justify-content-between align-items-center bg-transparent text-light border-secondary">
                                        Total Agents
                                        <span class="badge bg-primary rounded-pill">{{ agents|length }}</span>
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="card">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="card-title mb-0">Connected Agents</h5>
        <span class="badge bg-purple">{{ agents|length }} agents</span>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-hover table-dark">
                <thead>
                    <tr>
                        <th scope="col">ID</th>
                        <th scope="col">Hostname</th>
                        <th scope="col">Platform</th>
                        <th scope="col">IP Address</th>
                        <th scope="col">Last Seen</th>
                        <th scope="col">Status</th>
                        <th scope="col">Tasks</th>
                        <th scope="col">Actions</th>
                    </tr>
                </thead>
                <tbody>
                    {% for agent in agents %}
                    <tr>
                        <td class="font-monospace">{{ agent.id[:8] }}...</td>
                        <td>{{ agent.hostname }}</td>
                        <td>
                            {% if agent.platform == 'Windows' %}
                                <i class="fab fa-windows me-1"></i>
                            {% elif agent.platform == 'Linux' %}
                                <i class="fab fa-linux me-1"></i>
                            {% elif agent.platform == 'Darwin' %}
                                <i class="fab fa-apple me-1"></i>
                            {% else %}
                                <i class="fas fa-desktop me-1"></i>
                            {% endif %}
                            {{ agent.platform }}
                        </td>
                        <td>{{ agent.ip }}</td>
                        <td>{{ agent.last_seen }}</td>
                        <td>
                            {% if agent.status == 'active' %}
                                <span class="badge bg-success">Active</span>
                            {% else %}
                                <span class="badge bg-secondary">Offline</span>
                            {% endif %}
                        </td>
                        <td>
                            <span class="badge bg-info">{{ agent.task_count }}</span>
                            {% if agent.pending_tasks > 0 %}
                                <span class="badge bg-warning">{{ agent.pending_tasks }} pending</span>
                            {% endif %}
                        </td>
                        <td>
                            <div class="btn-group btn-group-sm">
                                <a href="{{ url_for('agent_detail', agent_id=agent.id) }}" class="btn btn-outline-info">
                                    <i class="fas fa-eye"></i>
                                </a>
                                <form method="POST" action="{{ url_for('delete_agent', agent_id=agent.id) }}" class="d-inline">
                                    <button type="submit" class="btn btn-outline-danger" onclick="return confirm('Are you sure you want to delete this agent?')">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </form>
                            </div>
                        </td>
                    </tr>
                    {% else %}
                    <tr>
                        <td colspan="8" class="text-center py-4">
                            <i class="fas fa-desktop fa-3x mb-3 text-muted"></i>
                            <p class="text-muted">No agents connected</p>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>
{% endblock %}