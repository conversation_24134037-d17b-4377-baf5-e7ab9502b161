<!-- templates/base.html -->
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PyC2 Framework</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --dark-bg: #1a1a1a;
            --darker-bg: #121212;
            --card-bg: #242424;
            --text-color: #e0e0e0;
            --accent-color: #8a2be2;
            --accent-hover: #9933ff;
        }
        
        body {
            background-color: var(--dark-bg);
            color: var(--text-color);
            font-family: 'Se<PERSON>e <PERSON>', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .navbar {
            background-color: var(--darker-bg) !important;
            border-bottom: 1px solid var(--accent-color);
        }
        
        .sidebar {
            background-color: var(--darker-bg);
            height: calc(100vh - 56px);
            position: fixed;
            top: 56px;
            padding-top: 1rem;
            border-right: 1px solid #333;
        }
        
        .sidebar .nav-link {
            color: var(--text-color);
            padding: 0.75rem 1rem;
            border-left: 3px solid transparent;
        }
        
        .sidebar .nav-link:hover {
            background-color: #2a2a2a;
            border-left: 3px solid var(--accent-color);
        }
        
        .sidebar .nav-link.active {
            background-color: #2a2a2a;
            border-left: 3px solid var(--accent-color);
            color: var(--accent-color);
        }
        
        .main-content {
            margin-left: 200px;
            padding: 20px;
            margin-top: 56px;
        }
        
        .card {
            background-color: var(--card-bg);
            border: 1px solid #333;
            color: var(--text-color);
        }
        
        .card-header {
            background-color: #2a2a2a;
            border-bottom: 1px solid #333;
        }
        
        .table {
            color: var(--text-color);
        }
        
        .table-hover tbody tr:hover {
            background-color: #2a2a2a;
        }
        
        .btn-primary {
            background-color: var(--accent-color);
            border-color: var(--accent-color);
        }
        
        .btn-primary:hover {
            background-color: var(--accent-hover);
            border-color: var(--accent-hover);
        }
        
        .status-badge {
            padding: 0.35em 0.65em;
            font-size: 0.75em;
            font-weight: 700;
            border-radius: 0.25rem;
        }
        
        .status-active {
            background-color: #198754;
            color: white;
        }
        
        .status-offline {
            background-color: #6c757d;
            color: white;
        }
        
        .form-control, .form-select {
            background-color: #2a2a2a;
            border: 1px solid #444;
            color: var(--text-color);
        }
        
        .form-control:focus, .form-select:focus {
            background-color: #2a2a2a;
            border-color: var(--accent-color);
            color: var(--text-color);
            box-shadow: 0 0 0 0.25rem rgba(138, 43, 226, 0.25);
        }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container-fluid">
            <a class="navbar-brand" href="#">
                <i class="fas fa-server me-2"></i>
                PyC2 Framework
            </a>
            <div class="d-flex">
                <span class="navbar-text me-3">
                    <i class="fas fa-user me-1"></i> {{ session.username }}
                </span>
                <a href="{{ url_for('logout') }}" class="btn btn-outline-light btn-sm">
                    <i class="fas fa-sign-out-alt me-1"></i> Logout
                </a>
            </div>
        </div>
    </nav>

    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-2 sidebar d-none d-md-block">
                <div class="list-group list-group-flush">
                    <a href="{{ url_for('dashboard') }}" class="nav-link {% if request.endpoint == 'dashboard' %}active{% endif %}">
                        <i class="fas fa-tachometer-alt me-2"></i> Dashboard
                    </a>
                    <a href="{{ url_for('agents') }}" class="nav-link {% if request.endpoint == 'agents' %}active{% endif %}">
                        <i class="fas fa-desktop me-2"></i> Agents
                    </a>
                    <a href="{{ url_for('tasks') }}" class="nav-link {% if request.endpoint == 'tasks' %}active{% endif %}">
                        <i class="fas fa-tasks me-2"></i> Tasks
                    </a>
                </div>
            </div>

            <!-- Main content -->
            <main class="col-md-10 ms-sm-auto main-content">
                {% block content %}{% endblock %}
            </main>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>