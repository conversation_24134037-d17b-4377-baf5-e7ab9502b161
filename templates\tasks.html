<!-- templates/tasks.html -->
{% extends "base.html" %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">Tasks Management</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <button type="button" class="btn btn-sm btn-outline-secondary" data-bs-toggle="modal" data-bs-target="#taskStatsModal">
                <i class="fas fa-chart-pie me-1"></i> View Stats
            </button>
        </div>
    </div>
</div>

<!-- Stats Modal -->
<div class="modal fade" id="taskStatsModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Task Statistics</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-md-12">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="card-title mb-0">Task Status Overview</h6>
                            </div>
                            <div class="card-body">
                                <ul class="list-group list-group-flush">
                                    {% for stat in task_stats %}
                                    <li class="list-group-item d-flex justify-content-between align-items-center bg-transparent text-light border-secondary">
                                        {{ stat.status|title }} Tasks
                                        <span class="badge 
                                            {% if stat.status == 'completed' %}bg-success
                                            {% elif stat.status == 'executing' %}bg-warning
                                            {% elif stat.status == 'pending' %}bg-secondary
                                            {% else %}bg-info{% endif %} rounded-pill">
                                            {{ stat.count }}
                                        </span>
                                    </li>
                                    {% endfor %}
                                    <li class="list-group-item d-flex justify-content-between align-items-center bg-transparent text-light border-secondary">
                                        Total Tasks
                                        <span class="badge bg-primary rounded-pill">{{ tasks|length }}</span>
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-4">
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="card-title mb-0">Send Command</h5>
            </div>
            <div class="card-body">
                <form method="POST" action="{{ url_for('send_command') }}">
                    <div class="mb-3">
                        <label for="agent_id" class="form-label">Select Agent</label>
                        <select class="form-select" id="agent_id" name="agent_id" required>
                            <option value="all">All Agents</option>
                            {% for agent in agents %}
                                <option value="{{ agent.id }}">{{ agent.hostname }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="command" class="form-label">Command</label>
                        <input type="text" class="form-control" id="command" name="command" placeholder="Enter command" required>
                    </div>
                    <button type="submit" class="btn btn-primary w-100">Execute</button>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-md-8">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">Task History</h5>
                <span class="badge bg-purple">{{ tasks|length }} tasks</span>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover table-dark">
                        <thead>
                            <tr>
                                <th scope="col">ID</th>
                                <th scope="col">Agent</th>
                                <th scope="col">Command</th>
                                <th scope="col">Created</th>
                                <th scope="col">Status</th>
                                <th scope="col">Result</th>
                                <th scope="col">Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for task in tasks %}
                            <tr>
                                <td class="font-monospace">#{{ task.id }}</td>
                                <td>
                                    <div class="d-flex align-items-center">
                                        {% if task.platform == 'Windows' %}
                                            <i class="fab fa-windows me-1 text-info"></i>
                                        {% elif task.platform == 'Linux' %}
                                            <i class="fab fa-linux me-1 text-success"></i>
                                        {% elif task.platform == 'Darwin' %}
                                            <i class="fab fa-apple me-1 text-light"></i>
                                        {% else %}
                                            <i class="fas fa-desktop me-1 text-muted"></i>
                                        {% endif %}
                                        {{ task.hostname }}
                                    </div>
                                </td>
                                <td class="text-truncate" style="max-width: 200px;">{{ task.command }}</td>
                                <td>{{ task.created_at }}</td>
                                <td>
                                    {% if task.status == 'completed' %}
                                        <span class="badge bg-success">Completed</span>
                                    {% elif task.status == 'executing' %}
                                        <span class="badge bg-warning">Executing</span>
                                    {% else %}
                                        <span class="badge bg-secondary">Pending</span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if task.result %}
                                        <button class="btn btn-sm btn-outline-info" data-bs-toggle="modal" data-bs-target="#resultModal{{ task.id }}">
                                            View
                                        </button>
                                        
                                        <!-- Result Modal -->
                                        <div class="modal fade" id="resultModal{{ task.id }}" tabindex="-1" aria-hidden="true">
                                            <div class="modal-dialog modal-lg">
                                                <div class="modal-content">
                                                    <div class="modal-header">
                                                        <h5 class="modal-title">Result for Task #{{ task.id }}</h5>
                                                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                                    </div>
                                                    <div class="modal-body">
                                                        <pre class="text-light bg-dark p-3 rounded">{{ task.result }}</pre>
                                                    </div>
                                                    <div class="modal-footer">
                                                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    {% else %}
                                        <span class="text-muted">No result</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <form method="POST" action="{{ url_for('delete_task', task_id=task.id) }}" class="d-inline">
                                        <button type="submit" class="btn btn-sm btn-outline-danger" onclick="return confirm('Are you sure you want to delete this task?')">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </form>
                                </td>
                            </tr>
                            {% else %}
                            <tr>
                                <td colspan="7" class="text-center py-4">
                                    <i class="fas fa-tasks fa-3x mb-3 text-muted"></i>
                                    <p class="text-muted">No tasks created yet</p>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}