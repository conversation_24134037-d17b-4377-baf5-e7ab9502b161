<!-- templates/agent_detail.html -->
{% extends "base.html" %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">Agent Details</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <a href="{{ url_for('agents') }}" class="btn btn-sm btn-outline-secondary">
                <i class="fas fa-arrow-left me-1"></i> Back to Agents
            </a>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-4">
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="card-title mb-0">Agent Information</h5>
            </div>
            <div class="card-body">
                <div class="d-flex align-items-center mb-4">
                    <div class="flex-shrink-0">
                        {% if agent.platform == 'Windows' %}
                            <i class="fab fa-windows fa-3x text-purple"></i>
                        {% elif agent.platform == 'Linux' %}
                            <i class="fab fa-linux fa-3x text-purple"></i>
                        {% elif agent.platform == 'Darwin' %}
                            <i class="fab fa-apple fa-3x text-purple"></i>
                        {% else %}
                            <i class="fas fa-desktop fa-3x text-purple"></i>
                        {% endif %}
                    </div>
                    <div class="flex-grow-1 ms-3">
                        <h4 class="mt-0 mb-1">{{ agent.hostname }}</h4>
                        <p class="mb-0 text-muted">{{ agent.id }}</p>
                    </div>
                </div>
                
                <ul class="list-group list-group-flush">
                    <li class="list-group-item d-flex justify-content-between align-items-center bg-transparent text-light border-secondary">
                        Platform
                        <span class="font-monospace">{{ agent.platform }}</span>
                    </li>
                    <li class="list-group-item d-flex justify-content-between align-items-center bg-transparent text-light border-secondary">
                        IP Address
                        <span class="font-monospace">{{ agent.ip }}</span>
                    </li>
                    <li class="list-group-item d-flex justify-content-between align-items-center bg-transparent text-light border-secondary">
                        Last Seen
                        <span>{{ agent.last_seen }}</span>
                    </li>
                    <li class="list-group-item d-flex justify-content-between align-items-center bg-transparent text-light border-secondary">
                        Status
                        <span>
                            {% if agent.status == 'active' %}
                                <span class="badge bg-success">Active</span>
                            {% else %}
                                <span class="badge bg-secondary">Offline</span>
                            {% endif %}
                        </span>
                    </li>
                </ul>
            </div>
        </div>
        
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">Send Command</h5>
            </div>
            <div class="card-body">
                <form method="POST" action="{{ url_for('send_command') }}">
                    <input type="hidden" name="agent_id" value="{{ agent.id }}">
                    <div class="mb-3">
                        <label for="command" class="form-label">Command</label>
                        <input type="text" class="form-control" id="command" name="command" placeholder="Enter command" required>
                    </div>
                    <button type="submit" class="btn btn-primary w-100">Execute</button>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-md-8">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">Task History</h5>
                <span class="badge bg-purple">{{ tasks|length }} tasks</span>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover table-dark">
                        <thead>
                            <tr>
                                <th scope="col">ID</th>
                                <th scope="col">Command</th>
                                <th scope="col">Created</th>
                                <th scope="col">Status</th>
                                <th scope="col">Result</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for task in tasks %}
                            <tr>
                                <td class="font-monospace">#{{ task.id }}</td>
                                <td class="text-truncate" style="max-width: 200px;">{{ task.command }}</td>
                                <td>{{ task.created_at }}</td>
                                <td>
                                    {% if task.status == 'completed' %}
                                        <span class="badge bg-success">Completed</span>
                                    {% elif task.status == 'executing' %}
                                        <span class="badge bg-warning">Executing</span>
                                    {% else %}
                                        <span class="badge bg-secondary">Pending</span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if task.result %}
                                        <button class="btn btn-sm btn-outline-info" data-bs-toggle="modal" data-bs-target="#resultModal{{ task.id }}">
                                            View
                                        </button>
                                        
                                        <!-- Result Modal -->
                                        <div class="modal fade" id="resultModal{{ task.id }}" tabindex="-1" aria-hidden="true">
                                            <div class="modal-dialog modal-lg">
                                                <div class="modal-content">
                                                    <div class="modal-header">
                                                        <h5 class="modal-title">Result for Task #{{ task.id }}</h5>
                                                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                                    </div>
                                                    <div class="modal-body">
                                                        <pre class="text-light bg-dark p-3 rounded">{{ task.result }}</pre>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    {% else %}
                                        <span class="text-muted">No result</span>
                                    {% endif %}
                                </td>
                            </tr>
                            {% else %}
                            <tr>
                                <td colspan="5" class="text-center py-4">
                                    <i class="fas fa-tasks fa-3x mb-3 text-muted"></i>
                                    <p class="text-muted">No tasks for this agent</p>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}